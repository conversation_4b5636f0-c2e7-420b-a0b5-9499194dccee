import decimal
import os
import shutil
from datetime import timed<PERSON>ta
from decimal import Decimal
from typing import Optional

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
from django.db import models
from django.db.models import Sum
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from extra_settings.models import Setting
# from notifications.models import Notification

from wms.cores.models import (
    DISPLAY_EMPTY_VALUE,
    AbstractBaseModel,
    AbstractDefectStockInModel,
    AbstractSystemNumberModel,
    AbstractSortableModel,
    AbstractStockInModel,
    greater_than_zero,
)
from wms.cores.utils import localtime_now, normalize_decimal

from wms.apps.settings.utils import uom_converter
from wms.integrated_clients.fmc.outbound_actions.fmc_edi_confirm import FMCEDIConfirmation

UPLOAD_PATH = "goods_received_notes"


class GoodsReceivedNote(AbstractSystemNumberModel, AbstractBaseModel):
    """GoodsReceivedNote model for Warehouse Management System.

    Available fields:

    * created               (AbstractBaseModel => TimeStampedModel)
    * modified              (AbstractBaseModel => TimeStampedModel)
    * created_by            (AbstractBaseModel)
    * modified_by           (AbstractBaseModel)
    * system_number         (AbstractSystemNumberModel)
    * issued_by
    * consignor
    * arrival_datetime
    * completion_datetime
    * deliver_to
    * status
    * customer_reference
    * remark
    * imported_file
    * container_date
    * container_number
    * container_size
    * container_seal_number
    * container_no_of_pallet
    * container_no_of_loose_carton
    * container_length
    * container_width
    * container_height
    * container_dimension_unit
    * container_cubicmeter
    * container_gross_weight_kg
    * container_gross_weight_tonn
    * is_edi_confirmation_sent
    * is_from_edi
    * consignor_inbound_delivery_no
    * available_for_racking
    * is_racking_completed

    """

    class Status(models.TextChoices):
        NEW = "New", _("New")
        PARTIALLY_RECEIVED = "Partially Received", _("Partially Received")
        OBSOLETE = "Obsolete", _("Obsolete")
        COMPLETED = "Completed", _("Completed")

    class DimensionUnit(models.TextChoices):
        # CM = "cm", _("cm")
        METER = "m", _("m")

    issued_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
    consignor = models.ForeignKey("consignors.Consignor", on_delete=models.PROTECT)
    arrival_datetime = models.DateTimeField(verbose_name=_("Arrival Date Time"), default=localtime_now)
    completion_datetime = models.DateTimeField(
        verbose_name=_("Completion Date Time"), blank=True, null=True, default=None
    )
    deliver_to = models.ForeignKey(
        "settings.Warehouse",
        related_name="goods_received_notes",
        limit_choices_to={"is_storage": True},
        on_delete=models.PROTECT,
    )
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.NEW)
    customer_reference = models.CharField(verbose_name=_("Customer Reference"), max_length=64, blank=True)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    imported_file = models.FileField(
        verbose_name="Imported File",
        blank=True,
        null=True,
        upload_to=UPLOAD_PATH,
        validators=[
            FileExtensionValidator(
                allowed_extensions=[
                    "txt",
                    "xml",
                ]
            )
        ],
    )
    container_date = models.DateField(verbose_name=_("Container Date"), blank=True, null=True)
    container_number = models.CharField(verbose_name=_("Container Number"), max_length=64, blank=True)
    container_size = models.CharField(verbose_name=_("Container Size"), max_length=64, blank=True)
    container_seal_number = models.CharField(verbose_name=_("Container Seal Number"), max_length=64, blank=True)
    container_no_of_pallet = models.IntegerField(verbose_name=_("No. Of Pallet"), blank=True, null=True)
    container_no_of_loose_carton = models.IntegerField(verbose_name=_("No. Of Loose Carton"), blank=True, null=True)
    # For Dimensions (Length X Width X Height)
    container_length = models.DecimalField(
        verbose_name=_("Dimension (Length)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    container_width = models.DecimalField(
        verbose_name=_("Dimension (Width)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    container_height = models.DecimalField(
        verbose_name=_("Dimension (Height)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    container_dimension_unit = models.CharField(
        verbose_name=_("Dimension Unit"), max_length=2, choices=DimensionUnit.choices, default=DimensionUnit.METER
    )
    container_cubicmeter = models.DecimalField(
        verbose_name=_("M3"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    container_gross_weight_kg = models.DecimalField(
        verbose_name=_("Gross weight / pallet (KG)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    container_gross_weight_tonn = models.DecimalField(
        verbose_name=_("Gross weight (T)"), max_digits=19, decimal_places=6, blank=True, null=True
    )
    is_edi_confirmation_sent = models.BooleanField(verbose_name=_("Is EDI Confirmation Sent?"), default=False)
    is_from_edi = models.BooleanField(
        verbose_name=_("Is From EDI?"),
        default=False,
        help_text=_("To differentiate it is manually created or from EDI"),
    )

    # consignor's side information
    consignor_inbound_delivery_no = models.CharField(verbose_name=_("Inbound Delivery No"), max_length=256, blank=True)
    # available_for_racking = models.BooleanField(
    #     verbose_name=_("Available for Racking"),
    #     default=False,
    #     help_text=_("To determine if a GRN is available for Racking."),
    # )
    # is_racking_completed = models.BooleanField(
    #     verbose_name=_("Is Racking Completed"),
    #     default=False,
    # )

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-system_number"]

    def __str__(self):
        return f"{self.system_number} :: {self.consignor}"

    def get_absolute_url(self):
        return reverse("receives:goods_received_notes:detail", kwargs={"pk": self.pk})

    @cached_property
    def check_completion_date_expiry(self):
        """
        Retrun Boolean on whether still allow COMPLETED GRN to be able to perform
        receives/rejects based on 14 days duration

        """
        allow_to_edit = False

        if self.completion_datetime is None:
            allow_to_edit = True
        elif self.completion_datetime:
            edit_period = self.completion_datetime + timedelta(days=14)
            if localtime_now() < edit_period:
                allow_to_edit = True

        return allow_to_edit

    @cached_property
    def total_all_stockin_system_quantity(self):
        """Get total count of stockin system quantity."""
        goods_receive_note_items_qs = self.goodsreceivednoteitem_set.all()
        total_all_stockin_system_quantity = 0
        total_all_defect_stockin_system_quantity = self.total_all_defect_stockin_system_quantity
        total_all_stockin_system_quantity_and_defect = 0

        for goods_receive_note_item in goods_receive_note_items_qs:
            total_stockin_system_quantity = goods_receive_note_item.goodsreceivednotestockin_set.all().aggregate(
                Sum("transaction__system_quantity")
            ).get("transaction__system_quantity__sum", Decimal("0")) or Decimal("0")

            total_all_stockin_system_quantity += round(
                total_stockin_system_quantity, goods_receive_note_item.item.uom.unit_precision
            )

        total_all_stockin_system_quantity_and_defect = (
            total_all_defect_stockin_system_quantity + total_all_stockin_system_quantity
        )

        return total_all_stockin_system_quantity_and_defect

    @cached_property
    def total_all_defect_stockin_system_quantity(self):
        """Get total count of stockin system quantity."""
        goods_receive_note_items_qs = self.goodsreceivednoteitem_set.all()

        total_all_defect_stockin_system_quantity = 0
        for goods_receive_note_item in goods_receive_note_items_qs:
            total_defect_stockin_system_quantity = (
                goods_receive_note_item.goodsreceivednotedefectstockin_set.all()
                .aggregate(Sum("transaction__system_quantity"))
                .get("transaction__system_quantity__sum", Decimal("0"))
                or Decimal("0")
            )

            total_all_defect_stockin_system_quantity += round(
                total_defect_stockin_system_quantity, goods_receive_note_item.item.uom.unit_precision
            )

        return total_all_defect_stockin_system_quantity

    @cached_property
    def total_all_stockin_expected_quantity(self):
        """Get total count of stockin expected quantity."""
        goods_receive_note_items_qs = self.goodsreceivednoteitem_set.all()

        total_all_stockin_expected_quantity = 0

        if goods_receive_note_items_qs:
            total_stockin_expected_quantity = goods_receive_note_items_qs.aggregate(Sum("quantity")).get(
                "quantity__sum", Decimal("0")
            ) or Decimal("0")

            total_all_stockin_expected_quantity += round(
                total_stockin_expected_quantity, goods_receive_note_items_qs[0].item.uom.unit_precision
            )

        return total_all_stockin_expected_quantity

    @cached_property
    def imported_file_filename(self):
        if self.imported_file:
            return os.path.basename(self.imported_file.name)
        else:
            return None

    # def clear_notifications(self) -> None:
    #     """
    #     Remove existing notifications for newly created GRN once Completed
    #     """
    #     if self.status == self.Status.COMPLETED:
    #         notifications_qs = Notification.objects.filter(
    #             actor_object_id=self.pk,
    #             actor_content_type=ContentType.objects.get_for_model(self),
    #             verb__icontains="NEW GRN created. Ready to check!",
    #             level="info",
    #             public=False,
    #         )

    #         if notifications_qs.exists():
    #             notifications_qs.delete()

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == GoodsReceivedNote.Status.NEW:
            html_class = "badge bg-theme-status-warning"
        elif self.status == GoodsReceivedNote.Status.PARTIALLY_RECEIVED:
            html_class = "badge bg-theme-status-info"
        elif self.status == GoodsReceivedNote.Status.OBSOLETE:
            html_class = "badge bg-gray-700"
        elif self.status == GoodsReceivedNote.Status.COMPLETED:
            html_class = "badge bg-theme-status-success"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE

    @cached_property
    def all_items_in_new_status(self):
        """Check if all release order items are in 'New' status."""
        return not self.goodsreceivednoteitem_set.all().exclude(status=GoodsReceivedNoteItem.Status.OPEN).exists()

    # @cached_property
    # def html_is_racking_completed_display(self):
    #     """Return nice HTML display for is_racking_completed."""

    #     if self.is_racking_completed is True:
    #         html_class = "fas fa-check-circle text-success"
    #     else:
    #         html_class = "fas fa-times-circle text-danger"

    #     return format_html(f'<span class="{html_class}"></span>') or DISPLAY_EMPTY_VALUE

    # @cached_property
    # def html_racking_progress_display(self) -> Optional[str]:
    #     """
    #     Determine the progress status of rackings for this object.

    #     Returns:
    #         - New (not started)
    #         - In Progress
    #         - Completed
    #         - DISPLAY_EMPTY_VALUE: Not applicable (e.g., not available for racking)
    #     """

    #     from wms.apps.rackings.models.rack import RackTransaction

    #     if not self.available_for_racking:
    #         return DISPLAY_EMPTY_VALUE

    #     if self.is_racking_completed:
    #         return format_html('<span class="fas fa-check-circle text-success"></span>')

    #     grn_items_pk = self.goodsreceivednoteitem_set.values_list("pk", flat=True)
    #     if RackTransaction.objects.filter(goods_received_note_item_id__in=grn_items_pk).exists():
    #         return format_html('<span class="fas fa-info-circle text-info"></span>')

    #     return format_html('<span class="fas fa-times-circle text-danger"></span>')

    def save(self, *args, **kwargs):
        """
        Override save method for GoodsReceivedNote.
        """
        # Automate M3 (container_cubicmeter) calculation.
        if self.container_length and self.container_width and self.container_height:
            self.container_cubicmeter = self.container_length * self.container_width * self.container_height

        if self.completion_datetime is None and self.status == self.Status.COMPLETED:
            self.completion_datetime = localtime_now()

        # Setting.get("EDI_ENABLED", default=False) must be first condition.
        if Setting.get("EDI_ENABLED", default=False):
            # MMM EDI
            if self.status == self.Status.COMPLETED and self.imported_file and self.consignor.code == "MMM":
                src_path = self.imported_file.path
                dst_path = settings.MITSUBISHI_FTP_SINO_STRF_RESULT_PATH
                shutil.copy(src_path, dst_path)

            # FMC EDI
            if (
                self.status == self.Status.COMPLETED
                and self.is_from_edi is True
                and self.imported_file
                and self.is_edi_confirmation_sent is False
                and self.consignor.code == "FMCSB"
            ):
                self.is_edi_confirmation_sent = True
                fmc_inbound_confirmation = FMCEDIConfirmation(obj=self)
                fmc_inbound_confirmation.send_xml_confirmation()

        # self.clear_notifications()

        # if self.status == self.Status.COMPLETED and self.deliver_to.rack_set.exists():
        #     self.available_for_racking = True

        super().save(*args, **kwargs)


class GoodsReceivedNoteItem(AbstractBaseModel, AbstractSortableModel):
    """GoodsReceivedNoteItem model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * sort_order        (AbstractSortableModel)
    * goods_received_note
    * item
    * uom
    * quantity
    * batch_no
    * expiry_date
    * shipment_number
    * pallet_number
    * remark
    * status

    """

    class Status(models.TextChoices):
        OPEN = "Open", _("Open")
        RECEIVED_PARTIALLY = "Received (Partially)", _("Received (Partially)")
        RECEIVED = "Received", _("Received")

    goods_received_note = models.ForeignKey("receives.GoodsReceivedNote", on_delete=models.CASCADE)
    item = models.ForeignKey("inventories.Item", on_delete=models.CASCADE)
    uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        verbose_name=_("UOM"),
        on_delete=models.RESTRICT,
        help_text=_("The item will be measured in terms of this unit (e.g.: kg, pcs, box)."),
    )
    quantity = models.DecimalField(
        verbose_name=_("Quantity"),
        max_digits=19,
        decimal_places=6,
        default=Decimal("0"),
        validators=[greater_than_zero],
    )
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, blank=True)
    expiry_date = models.DateField(verbose_name=_("Expiry Date"), blank=True, null=True)
    shipment_number = models.CharField(verbose_name=_("Shipment Number"), max_length=64, blank=True)
    pallet_number = models.CharField(verbose_name=_("Pallet Number"), max_length=64, blank=True)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.OPEN)
    # scenario on FMC EDI, some item such as big machine, they are using "serialNumber" instead of using "batchNumber"
    # (but in the consideration of our structure, batch_no should serve this purpose to store this number)
    # in the inbound/outbound return confirmation we need to prepare different naming tag for them.
    # this field serves the indicator.
    is_serial_no = models.BooleanField(verbose_name=_("Is Serial Number?"), default=False)

    # is_cleaned = False

    class Meta(AbstractBaseModel.Meta):
        ordering = ["sort_order"]

    def __str__(self):
        return f"{self.goods_received_note.system_number} :: {self.item.code} :: {self.item.name} :: {self.quantity}"

    @cached_property
    def get_distinct_item_batch_no_qs(self):
        """Return distinct item's batch_no queryset."""

        distinct_item_batch_no_qs = self.goodsreceivednotestockin_set.all().order_by("batch_no").distinct("batch_no")

        return distinct_item_batch_no_qs

    @cached_property
    def get_distinct_item_batch_no_list(self):
        """Return distinct item's batch_no quantity."""

        distinct_item_batch_no_qs = self.get_distinct_item_batch_no_qs

        temp_list = []

        for distinct_item_batch_no_obj in distinct_item_batch_no_qs:

            temp_dict = {}

            distinct_item_batch_no_stockin_converted_quantity = self.goodsreceivednotestockin_set.filter(
                item=distinct_item_batch_no_obj.item, batch_no=distinct_item_batch_no_obj.batch_no
            ).aggregate(Sum("converted_quantity")).get("converted_quantity__sum", Decimal("0")) or Decimal("0")

            convert_converted_quantity = uom_converter(
                origin_uom=self.item.uom,
                target_uom=self.uom,
                quantity=distinct_item_batch_no_stockin_converted_quantity,
            )

            temp_dict["batch_no"] = distinct_item_batch_no_obj.batch_no
            temp_dict["total_quantity"] = convert_converted_quantity

            temp_list.append(temp_dict)

        return temp_list

    @cached_property
    def get_distinct_defect_item_batch_no_qs(self):
        """Return distinct item's batch_no queryset."""

        distinct_defect_item_batch_no_qs = (
            self.goodsreceivednotedefectstockin_set.all().order_by("batch_no", "reason").distinct("batch_no", "reason")
        )

        return distinct_defect_item_batch_no_qs

    @cached_property
    def get_distinct_defect_item_batch_no_list(self):
        """Return distinct item's batch_no quantity."""

        distinct_defect_item_batch_no_qs = self.get_distinct_defect_item_batch_no_qs

        temp_list = []

        for distinct_defect_item_batch_no_obj in distinct_defect_item_batch_no_qs:

            temp_dict = {}

            distinct_item_batch_no_stockin_converted_quantity = self.goodsreceivednotedefectstockin_set.filter(
                item=distinct_defect_item_batch_no_obj.item,
                reason=distinct_defect_item_batch_no_obj.reason,
                batch_no=distinct_defect_item_batch_no_obj.batch_no,
            ).aggregate(Sum("converted_quantity")).get("converted_quantity__sum", Decimal("0")) or Decimal("0")

            convert_converted_quantity = uom_converter(
                origin_uom=self.item.uom,
                target_uom=self.uom,
                quantity=distinct_item_batch_no_stockin_converted_quantity,
            )

            temp_dict["batch_no"] = distinct_defect_item_batch_no_obj.batch_no
            temp_dict["reason"] = distinct_defect_item_batch_no_obj.get_reason_display()
            temp_dict["total_quantity"] = convert_converted_quantity

            temp_list.append(temp_dict)

        return temp_list

    @cached_property
    def received_percentage(self):
        try:
            return round((self.get_received_quantity / self.quantity) * Decimal("100"), 0)
        except (decimal.DivisionByZero, decimal.InvalidOperation):
            return 0

    @cached_property
    def get_received_quantity(self):
        """Return current item's received quantity."""

        received_quantity = self.goodsreceivednotestockin_set.all().aggregate(Sum("approved_quantity")).get(
            "approved_quantity__sum", Decimal("0")
        ) or Decimal("0")

        received_quantity = round(received_quantity, self.uom.unit_precision)
        return received_quantity

    @cached_property
    def get_rejected_quantity(self):
        """Return current item's rejected quantity"""

        rejected_quantity = self.goodsreceivednotedefectstockin_set.all().aggregate(Sum("approved_quantity")).get(
            "approved_quantity__sum", Decimal("0")
        ) or Decimal("0")

        rejected_quantity = round(rejected_quantity, self.uom.unit_precision)
        return rejected_quantity

    # @cached_property
    # def get_stock_in_racking_quantity(self):
    #     from wms.apps.rackings.models.rack import RackTransaction

    #     stock_in_quantity = RackTransaction.objects.filter(goods_received_note_item=self).aggregate(
    #         Sum("quantity")
    #     ).get("quantity__sum", Decimal("0")) or Decimal("0")

    #     return stock_in_quantity

    @cached_property
    def get_transaction_received_quantity(self):
        """Return current item's received quantity based on transaction's system quantity."""

        transaction_system_quantity = self.goodsreceivednotestockin_set.all().aggregate(
            Sum("transaction__system_quantity")
        ).get("transaction__system_quantity__sum", Decimal("0")) or Decimal("0")

        transaction_system_quantity = round(transaction_system_quantity, self.item.uom.unit_precision)
        return transaction_system_quantity

    @cached_property
    def get_stockin_converted_received_quantity(self):
        """Return current item's received quantity based on transaction's system quantity."""

        stockin_converted_quantity = self.goodsreceivednotestockin_set.all().aggregate(Sum("converted_quantity")).get(
            "converted_quantity__sum", Decimal("0")
        ) or Decimal("0")

        convert_converted_quantity = uom_converter(
            origin_uom=self.item.uom,
            target_uom=self.uom,
            quantity=stockin_converted_quantity,
        )

        return convert_converted_quantity

    @cached_property
    def get_defect_stockin_converted_received_quantity(self):
        """Return current item's DEFECT received quantity based on transaction's system quantity."""

        defect_stockin_converted_quantity = self.goodsreceivednotedefectstockin_set.all().aggregate(
            Sum("converted_quantity")
        ).get("converted_quantity__sum", Decimal("0")) or Decimal("0")

        convert_converted_quantity = uom_converter(
            origin_uom=self.item.uom,
            target_uom=self.uom,
            quantity=defect_stockin_converted_quantity,
        )

        convert_converted_quantity = round(convert_converted_quantity, self.uom.unit_precision)

        return convert_converted_quantity

    @cached_property
    def get_defect_converted_rejected_quantity(self):
        """Return current item's converted rejected quantity"""

        defect_converted_rejected_quantity = self.goodsreceivednotedefect_set.all().aggregate(
            Sum("converted_rejected_quantity")
        ).get("converted_rejected_quantity__sum", Decimal("0")) or Decimal("0")

        convert_converted_rejected_quantity = uom_converter(
            origin_uom=self.item.uom,
            target_uom=self.uom,
            quantity=defect_converted_rejected_quantity,
        )

        return convert_converted_rejected_quantity

    @cached_property
    def get_total_received_rejected_quantity(self):
        """Return current item's total of received quantity and rejected quantity."""
        total_received_rejected_quantity = (
            # self.get_stockin_converted_received_quantity + self.get_defect_converted_rejected_quantity
            self.get_stockin_converted_received_quantity
            + self.get_defect_stockin_converted_received_quantity
        )
        return total_received_rejected_quantity

    @cached_property
    def quantity_status_dict(self):
        """Get a dictionary of quantity status.

        Expected return e.g:
            quantity_status_dict = {
                "received_quantity": 60,
                "rejected_quantity": 40,
                "total_received_rejected_quantity": 100,
                "expected_quantity": 100,
                "received_percentage": 60,
                "rejected_percentage": 40,
                "total_percentage": 100,
                "received_status": "success",
                "overall_status": "warning",
            }
        """
        # Use transaction received quantity
        received_quantity = self.get_stockin_converted_received_quantity
        rejected_quantity = self.get_defect_stockin_converted_received_quantity
        total_received_rejected_quantity = self.get_total_received_rejected_quantity
        expected_quantity = round(self.quantity, self.uom.unit_precision)
        received_percentage = round((received_quantity / expected_quantity) * Decimal("100"), 0)
        rejected_percentage = round((rejected_quantity / expected_quantity) * Decimal("100"), 0)
        total_percentage = round((total_received_rejected_quantity / expected_quantity) * Decimal("100"), 0)

        overall_status = "danger"
        if total_percentage == 100:
            overall_status = "success"
        elif total_percentage > 100:
            overall_status = "primary"
        elif total_percentage > 0:
            overall_status = "warning"

        received_status = "warning"
        if received_percentage == 100:
            received_status = "success"
        elif received_percentage > 100:
            received_status = "primary"

        quantity_status_dict = {
            "received_quantity": received_quantity,
            "rejected_quantity": rejected_quantity,
            "total_received_rejected_quantity": total_received_rejected_quantity,
            "expected_quantity": expected_quantity,
            "received_percentage": received_percentage,
            "rejected_percentage": rejected_percentage,
            "total_percentage": total_percentage,
            "overall_status": overall_status,
            "received_status": received_status,
        }

        return quantity_status_dict

    # @cached_property
    # def racking_quantity_status_dict(self):
    #     """Get a dictionary of quantity status for Stock In to Racking.

    #     Expected return e.g:
    #         quantity_status_dict = {
    #             "stock_in_quantity": 60,
    #             "expected_quantity": 100,
    #             "remaining_quantity": 40,
    #             "stock_in_percentage": 60,
    #             "remaining_percentage": 40,
    #             "total_percentage": 100,
    #             "stock_in_status": "success",
    #             "overall_status": "warning",
    #         }
    #     """
    #     stock_in_quantity = normalize_decimal(self.get_stock_in_racking_quantity)
    #     expected_quantity = normalize_decimal(self.get_total_received_rejected_quantity)
    #     remaining_quantity = expected_quantity - stock_in_quantity
    #     stock_in_percentage = round((stock_in_quantity / expected_quantity) * Decimal("100"), 0)
    #     remaining_percentage = round((remaining_quantity / expected_quantity) * Decimal("100"), 0)
    #     total_percentage = round(((stock_in_quantity + remaining_quantity) / expected_quantity) * Decimal("100"), 0)

    #     overall_status = "danger"
    #     if total_percentage == 100:
    #         overall_status = "success"
    #     elif total_percentage > 100:
    #         overall_status = "primary"
    #     elif total_percentage > 0:
    #         overall_status = "warning"

    #     stock_in_status = "warning"
    #     if stock_in_percentage == 100:
    #         stock_in_status = "success"
    #     elif remaining_percentage > 100:
    #         stock_in_status = "primary"

    #     quantity_status_dict = {
    #         "stock_in_quantity": stock_in_quantity,
    #         "remaining_quantity": remaining_quantity,
    #         "expected_quantity": expected_quantity,
    #         "stock_in_percentage": stock_in_percentage,
    #         "remaining_percentage": remaining_percentage,
    #         "total_percentage": total_percentage,
    #         "overall_status": overall_status,
    #         "stock_in_status": stock_in_status,
    #     }

    #     return quantity_status_dict

    # @cached_property
    # def html_racks_display(self):
    #     from wms.apps.rackings.models.rack import RackTransaction

    #     rack_transactions = RackTransaction.objects.filter(goods_received_note_item=self).order_by(
    #         "transaction_datetime"
    #     )
    #     li_html = ""

    #     for transaction in rack_transactions:
    #         link = reverse(
    #             "inventories:racks:transaction_detail",
    #             kwargs={
    #                 "rack_pk": transaction.rackstorage.rack.pk,
    #                 "pk": transaction.rackstorage.stock.pk,
    #                 "year": transaction.transaction_datetime.year,
    #                 "month": transaction.transaction_datetime.month,
    #             },
    #         )

    #         li_html += (
    #             f"<li class='showmore-item'>"
    #             f"<a href='{link}' target='_blank'>{transaction.rackstorage.rack.full_name.replace(' ', '&nbsp;')}</a>"
    #             f" [{normalize_decimal(transaction.quantity)}]</li>"
    #         )

    #     if li_html:
    #         return format_html(f"<ul class='list-unstyled mb-0 showmore-items'>{li_html}</li>")
    #     else:
    #         return DISPLAY_EMPTY_VALUE

    @cached_property
    def running_number(self):
        """Return running number as in imported txt."""
        return str(self.sort_order).zfill(5)

    @cached_property
    def get_position(self):
        """Return position of item in GRN."""
        position = (
            list(
                self.__class__.objects.filter(goods_received_note=self.goods_received_note)
                .order_by("sort_order")
                .values_list("pk", flat=True)
            ).index(self.pk)
            + 1
        )

        return position

    # def clean(self):
    #     """Validation on item. Item must belongs to goods_received_note.consignor."""
    #     self.is_cleaned = True

    #     if self.item not in self.goods_received_note.consignor.items:
    #         raise ValidationError(_(f"Item does not belongs to consignor {self.goods_received_note.consignor}."))

    #     super().clean()

    def save(self, *args, **kwargs):
        """Override save for GoodsReceivedNoteItem.

        Set status of goods_received_note based on all related GoodsReceivedNoteItem's status.
        """
        model = self.__class__

        # Auto calculate sort_order
        if self.sort_order == 0:
            try:
                last = model.objects.filter(goods_received_note=self.goods_received_note).order_by("-sort_order")[0]
                self.sort_order = last.sort_order + 1
            except IndexError:
                # This item is first row
                self.sort_order = 1

        # if not self.is_cleaned:
        #     self.full_clean()

        super().save(*args, **kwargs)

        if self.status == GoodsReceivedNoteItem.Status.RECEIVED:
            all_goodsreceivednoteitem = self.goods_received_note.goodsreceivednoteitem_set.all()
            all_open_goodsreceivednoteitem = all_goodsreceivednoteitem.filter(status=GoodsReceivedNoteItem.Status.OPEN)
            all_received_partially_goodsreceivednoteitem = all_goodsreceivednoteitem.filter(
                status=GoodsReceivedNoteItem.Status.RECEIVED_PARTIALLY
            )

            if all_open_goodsreceivednoteitem.count() > 0 or all_received_partially_goodsreceivednoteitem.count() > 0:
                self.goods_received_note.status = GoodsReceivedNote.Status.PARTIALLY_RECEIVED
            else:
                self.goods_received_note.status = GoodsReceivedNote.Status.COMPLETED
            # self.goods_received_note.save(update_fields=["status", "is_edi_confirmation_sent", "available_for_racking"])
            self.goods_received_note.save(update_fields=["status", "is_edi_confirmation_sent"])

        elif self.status == GoodsReceivedNoteItem.Status.RECEIVED_PARTIALLY:
            self.goods_received_note.status = GoodsReceivedNote.Status.PARTIALLY_RECEIVED
            self.goods_received_note.save(update_fields=["status"])


class StockInRejectMixin:
    """Mixin for GoodsReceivedNoteStockIn and GoodsReceivedNoteDefect"""

    def save(self, *args, **kwargs):
        """Override save for GoodsReceivedNoteStockIn and GoodsReceivedNoteDefect.

        Set status of goods_received_note_item based on total_received_rejected_quantity.
        """
        if not self.is_cleaned:
            self.full_clean()

        super().save(*args, **kwargs)

        total_received_rejected_quantity = self.goods_received_note_item.get_total_received_rejected_quantity

        if total_received_rejected_quantity >= self.goods_received_note_item.quantity:
            self.goods_received_note_item.status = GoodsReceivedNoteItem.Status.RECEIVED
        elif total_received_rejected_quantity > 0:
            self.goods_received_note_item.status = GoodsReceivedNoteItem.Status.RECEIVED_PARTIALLY

        self.goods_received_note_item.save(update_fields=["status"])


class GoodsReceivedNoteStockIn(StockInRejectMixin, AbstractBaseModel, AbstractStockInModel):
    """GoodsReceivedNoteStockIn model for Warehouse Management System.

    Available fields:

    * created                           (AbstractBaseModel => TimeStampedModel)
    * modified                          (AbstractBaseModel => TimeStampedModel)
    * created_by                        (AbstractBaseModel)
    * modified_by                       (AbstractBaseModel)
    * deliver_to                        (AbstractStockInModel)
    * approved_by                       (AbstractStockInModel)
    * approved_quantity                 (AbstractStockInModel)
    * batch_no                          (AbstractStockInModel)
    * expiry_date                       (AbstractStockInModel)
    * stock_in_datetime                 (AbstractStockInModel)
    * item                              (AbstractStockInModel)
    * uom                               (AbstractStockInModel)
    * remark                            (AbstractStockInModel)
    * transaction                       (AbstractStockInModel)
    * goods_received_note_item
    * unit_price
    * total_price
    * converted_quantity
    * converted_uom

    """

    goods_received_note_item = models.ForeignKey(
        "receives.GoodsReceivedNoteItem",
        on_delete=models.PROTECT,
        help_text=_("Each Receive Note Item indicate a relation to item's delievered quantity."),
    )
    unit_price = models.DecimalField(
        verbose_name=_("Cost Price"), max_digits=19, decimal_places=4, default=Decimal("0")
    )
    total_price = models.DecimalField(
        verbose_name=_("Total Price"),
        max_digits=19,
        decimal_places=4,
        default=Decimal("0"),
        help_text=_("Total Price = Quantity * unit_price."),
    )
    converted_quantity = models.DecimalField(
        verbose_name=_("Converted Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    converted_uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        related_name="converted_uoms",
        verbose_name=_("Converted UOM"),
        on_delete=models.RESTRICT,
        blank=True,
        null=True,
    )

    is_cleaned = False

    class Meta(AbstractBaseModel.Meta):
        ordering = ["stock_in_datetime", "pk"]

    def __str__(self):
        return f"{self.item.name} :: {self.deliver_to.name} :: {self.approved_quantity}"

    def save(self, *args, **kwargs):
        """
        Override save method for GoodsReceivedNote.
        """

        calculated_converted_quantity = uom_converter(
            origin_uom=self.uom,
            target_uom=self.item.uom,
            quantity=self.approved_quantity,
        )

        self.converted_quantity = calculated_converted_quantity
        self.converted_uom = self.item.uom

        super().save(*args, **kwargs)


class GoodsReceivedNoteDefect(StockInRejectMixin, AbstractBaseModel):
    """GoodsReceivedNoteDefect model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * reason
    * goods_received_note_item
    * deliver_to
    * rejected_by
    * rejected_quantity
    * converted_rejected_quantity
    * converted_rejected_uom
    * batch_no
    * defect_datetime
    * item
    * uom
    * remark
    * is_migrated

    """

    class Reason(models.TextChoices):
        DAMAGED = "DAMAGED", _("Damaged")
        DENTED = "DENTED", _("Dented")
        EXPIRED = "EXPIRED", _("Expired")
        NOTREC = "NOTRECEIVE", _("Not Receive")
        OTHERS = "OTHERS", _("Others")

    reason = models.CharField(verbose_name=_("Reason"), max_length=10, choices=Reason.choices)
    goods_received_note_item = models.ForeignKey(
        "receives.GoodsReceivedNoteItem",
        on_delete=models.PROTECT,
        help_text=_("Each Receive Note Item indicate a relation to item's rejected quantity."),
    )
    deliver_to = models.ForeignKey(
        "settings.Warehouse", limit_choices_to={"is_storage": True}, on_delete=models.PROTECT
    )
    rejected_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT)
    rejected_quantity = models.DecimalField(
        verbose_name=_("Rejected Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    converted_rejected_quantity = models.DecimalField(
        verbose_name=_("Converted Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    converted_rejected_uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        related_name="converted_rejected_uoms",
        verbose_name=_("Converted UOM"),
        on_delete=models.RESTRICT,
        blank=True,
        null=True,
    )
    batch_no = models.CharField(verbose_name=_("Batch No."), max_length=255, default="N/A")
    defect_datetime = models.DateTimeField(verbose_name=_("Defect Date Time"))
    item = models.ForeignKey("inventories.Item", on_delete=models.RESTRICT)
    uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        related_name="uoms",
        verbose_name=_("UOM"),
        on_delete=models.RESTRICT,
        help_text=_("UOM for rejected item. Limited to EA for now."),
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    is_migrated = models.BooleanField(
        default=False,
        verbose_name=_("Is Migrated?"),
        help_text=_("Designates whether it has been migrated into GoodsReceivedNoteDefectStockIn with Transaction."),
    )

    is_cleaned = False

    class Meta(AbstractBaseModel.Meta):
        ordering = ["defect_datetime", "pk"]

    def __str__(self):
        return f"{self.item.name} :: {self.deliver_to.name} :: {self.rejected_quantity}"

    def clean(self):
        """Validation on item and rejected quantity."""
        self.is_cleaned = True

        if self.item != self.goods_received_note_item.item:
            raise ValidationError(_("Item does not match."))

        if self.rejected_quantity.is_zero() is True:
            raise ValidationError({"rejected_quantity": _("Quantity cannot be 0.")})

        super().clean()

    def save(self, *args, **kwargs):
        """
        Override save method for GoodsReceivedNote.
        """

        calculated_converted_rejected_quantity = uom_converter(
            origin_uom=self.uom,
            target_uom=self.item.uom,
            quantity=self.rejected_quantity,
        )

        self.converted_rejected_quantity = calculated_converted_rejected_quantity
        self.converted_rejected_uom = self.item.uom

        super().save(*args, **kwargs)


class GoodsReceivedNoteDefectStockIn(StockInRejectMixin, AbstractBaseModel, AbstractDefectStockInModel):
    """GoodsReceivedNoteDefectStockIn model for Warehouse Management System.

    Available fields:

    * created                           (AbstractBaseModel => TimeStampedModel)
    * modified                          (AbstractBaseModel => TimeStampedModel)
    * created_by                        (AbstractBaseModel)
    * modified_by                       (AbstractBaseModel)
    * deliver_to                        (AbstractDefectStockInModel)
    * approved_by                       (AbstractDefectStockInModel)
    * approved_quantity                 (AbstractDefectStockInModel)
    * batch_no                          (AbstractDefectStockInModel)
    * expiry_date                       (AbstractDefectStockInModel)
    * stock_in_datetime                 (AbstractDefectStockInModel)
    * item                              (AbstractDefectStockInModel)
    * uom                               (AbstractDefectStockInModel)
    * remark                            (AbstractDefectStockInModel)
    * transaction                       (AbstractDefectStockInModel)
    * reason
    * goods_received_note_item
    * unit_price
    * total_price
    * converted_quantity
    * converted_uom

    """

    class Reason(models.TextChoices):
        DAMAGED = "DAMAGED", _("Damaged")
        DENTED = "DENTED", _("Dented")
        EXPIRED = "EXPIRED", _("Expired")
        NOTREC = "NOTRECEIVE", _("Not Receive")
        OTHERS = "OTHERS", _("Others")

    reason = models.CharField(verbose_name=_("Reason"), max_length=10, choices=Reason.choices)
    goods_received_note_item = models.ForeignKey(
        "receives.GoodsReceivedNoteItem",
        on_delete=models.PROTECT,
        help_text=_("Each Receive Note Item indicate a relation to item's delievered quantity."),
    )
    unit_price = models.DecimalField(
        verbose_name=_("Cost Price"), max_digits=19, decimal_places=4, default=Decimal("0")
    )
    total_price = models.DecimalField(
        verbose_name=_("Total Price"),
        max_digits=19,
        decimal_places=4,
        default=Decimal("0"),
        help_text=_("Total Price = Quantity * unit_price."),
    )
    converted_quantity = models.DecimalField(
        verbose_name=_("Converted Quantity"), max_digits=19, decimal_places=6, default=Decimal("0")
    )
    converted_uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        related_name="defect_converted_uoms",
        verbose_name=_("Converted UOM"),
        on_delete=models.RESTRICT,
        blank=True,
        null=True,
    )

    is_cleaned = False

    class Meta(AbstractBaseModel.Meta):
        ordering = ["stock_in_datetime", "pk"]

    def __str__(self):
        return f"{self.item.name} :: {self.deliver_to.name} :: {self.approved_quantity}"

    def save(self, *args, **kwargs):
        """
        Override save method for GoodsReceivedNote.
        """

        calculated_converted_quantity = uom_converter(
            origin_uom=self.uom,
            target_uom=self.item.uom,
            quantity=self.approved_quantity,
        )

        self.converted_quantity = round(calculated_converted_quantity, self.item.uom.unit_precision)
        self.converted_uom = self.item.uom

        super().save(*args, **kwargs)
